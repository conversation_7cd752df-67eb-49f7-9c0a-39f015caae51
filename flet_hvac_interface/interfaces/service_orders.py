"""
🔧 Service Orders Interface - Kanban Workflow Management
Advanced service order tracking with drag-and-drop Kanban board and AI-powered dispatch
"""

import flet as ft
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json

logger = logging.getLogger(__name__)

class ServiceOrdersInterface:
    """
    Service orders interface with Kanban workflow management
    Features drag-and-drop boards, AI-powered dispatch, and real-time updates
    """
    
    def __init__(self, page: ft.Page, threading_manager=None, semantic_framework=None):
        self.page = page
        self.threading_manager = threading_manager
        self.semantic_framework = semantic_framework
        self.container = None
        self.selected_order = None
        self.view_mode = "kanban"  # kanban or list
        
        # Zaawansowane statusy Kanban dla HVAC
        self.kanban_stages = [
            {"id": "nowe_zgloszenie", "name": "Nowe Zgłoszenie", "color": ft.colors.BLUE_GREY, "icon": ft.icons.NEW_RELEASES},
            {"id": "wycena", "name": "<PERSON><PERSON><PERSON>na", "color": ft.colors.AMBER, "icon": ft.icons.CALCULATE},
            {"id": "zaplanowane", "name": "Zaplanowane", "color": ft.colors.BLUE, "icon": ft.icons.SCHEDULE},
            {"id": "w_trakcie", "name": "W Trakcie", "color": ft.colors.ORANGE, "icon": ft.icons.BUILD},
            {"id": "oczekiwanie_czesci", "name": "Oczekiwanie na Części", "color": ft.colors.PURPLE, "icon": ft.icons.INVENTORY},
            {"id": "testowanie", "name": "Testowanie Systemu", "color": ft.colors.CYAN, "icon": ft.icons.SCIENCE},
            {"id": "kontrola_jakosci", "name": "Kontrola Jakości", "color": ft.colors.TEAL, "icon": ft.icons.VERIFIED},
            {"id": "odbiory_techniczne", "name": "Odbiory Techniczne", "color": ft.colors.INDIGO, "icon": ft.icons.ASSIGNMENT_TURNED_IN},
            {"id": "zakonczone", "name": "Zakończone", "color": ft.colors.GREEN, "icon": ft.icons.CHECK_CIRCLE},
            {"id": "gwarancja", "name": "Okres Gwarancji", "color": ft.colors.LIGHT_GREEN, "icon": ft.icons.SHIELD},
            {"id": "rozliczone", "name": "Rozliczone", "color": ft.colors.DEEP_PURPLE, "icon": ft.icons.PAYMENT},
            {"id": "archiwum", "name": "Archiwum", "color": ft.colors.GREY_700, "icon": ft.icons.ARCHIVE}
        ]
        
        # Przykładowe zlecenia serwisowe HVAC
        self.sample_orders = [
            {
                "id": "ZS-2024-001",
                "customer_name": "Jan Kowalski",
                "customer_id": 1,
                "title": "Serwis klimatyzacji - Biurowiec",
                "description": "Przegląd okresowy systemu Daikin VRV z wymianą filtrów",
                "priority": "normal",
                "status": "zaplanowane",
                "category": "serwis",
                "assigned_technician": "Tomasz Nowak",
                "scheduled_date": datetime.now() + timedelta(days=1),
                "estimated_duration": 4,
                "equipment_type": "Daikin VRV IV-S",
                "equipment_model": "RXYSQ8T7V1B",
                "location": "ul. Marszałkowska 1, 00-001 Warszawa",
                "value": 850.00,
                "created_date": datetime.now() - timedelta(days=2),
                "tags": ["serwis", "daikin", "biurowiec", "przegląd"],
                "hvac_type": "VRV",
                "refrigerant": "R-32",
                "warranty_status": "w_gwarancji"
            },
            {
                "id": "ZS-2024-002",
                "customer_name": "Anna Nowak",
                "customer_id": 2,
                "title": "Awaryjna naprawa - Restauracja",
                "description": "Klimatyzacja nie chłodzi, pilna naprawa sprężarki",
                "priority": "high",
                "status": "w_trakcie",
                "category": "serwis",
                "assigned_technician": "Marek Kowalczyk",
                "scheduled_date": datetime.now(),
                "estimated_duration": 3,
                "equipment_type": "LG Multi V 5",
                "equipment_model": "ARUB360LTE5",
                "location": "ul. Nowy Świat 15, 00-029 Warszawa",
                "value": 1200.00,
                "created_date": datetime.now() - timedelta(hours=6),
                "tags": ["awaria", "naprawa", "lg", "restauracja", "sprężarka"],
                "hvac_type": "Multi Split",
                "refrigerant": "R-410A",
                "warranty_status": "po_gwarancji"
            },
            {
                "id": "ZI-2024-003",
                "customer_name": "Piotr Wiśniewski",
                "customer_id": 3,
                "title": "Nowa instalacja - Pokoje hotelowe",
                "description": "Montaż 8 jednostek klimatyzacyjnych w pokojach hotelowych",
                "priority": "normal",
                "status": "oczekiwanie_czesci",
                "category": "nowa_instalacja",
                "assigned_technician": "Paweł Zieliński",
                "scheduled_date": datetime.now() + timedelta(days=5),
                "estimated_duration": 16,
                "equipment_type": "Mitsubishi Electric City Multi",
                "equipment_model": "PUMY-P140VKM",
                "location": "ul. Krakowskie Przedmieście 5, 00-068 Warszawa",
                "value": 15600.00,
                "created_date": datetime.now() - timedelta(days=7),
                "tags": ["instalacja", "mitsubishi", "hotel", "multi_split"],
                "hvac_type": "City Multi",
                "refrigerant": "R-32",
                "warranty_status": "nowy_sprzet"
            },
            {
                "id": "ZO-2024-004",
                "customer_name": "Maria Kowalska",
                "customer_id": 4,
                "title": "Oględziny systemu chłodniczego",
                "description": "Roczne oględziny i certyfikacja systemu Carrier",
                "priority": "low",
                "status": "nowe_zgloszenie",
                "category": "ogledziny",
                "assigned_technician": None,
                "scheduled_date": None,
                "estimated_duration": 2,
                "equipment_type": "Carrier 30RB Chiller",
                "equipment_model": "30RB0302",
                "location": "ul. Żurawia 10, 00-503 Warszawa",
                "value": 450.00,
                "created_date": datetime.now() - timedelta(days=1),
                "tags": ["oględziny", "carrier", "certyfikacja", "chiller"],
                "hvac_type": "Chiller",
                "refrigerant": "R-134a",
                "warranty_status": "po_gwarancji"
            },
            {
                "id": "ZO-2024-005",
                "customer_name": "Firma ABC Sp. z o.o.",
                "customer_id": 5,
                "title": "Oględziny przed modernizacją",
                "description": "Ocena stanu istniejącej instalacji przed modernizacją",
                "priority": "normal",
                "status": "zaplanowane",
                "category": "ogledziny",
                "assigned_technician": "Krzysztof Wiśniewski",
                "scheduled_date": datetime.now() + timedelta(days=3),
                "estimated_duration": 3,
                "equipment_type": "Stary system split",
                "equipment_model": "Różne modele",
                "location": "ul. Puławska 120, 02-566 Warszawa",
                "value": 600.00,
                "created_date": datetime.now() - timedelta(days=3),
                "tags": ["oględziny", "modernizacja", "ocena", "split"],
                "hvac_type": "Split",
                "refrigerant": "R-22",
                "warranty_status": "stary_sprzet"
            }
        ]
        
        logger.info("🔧 Service Orders Interface initialized")
    
    def create_interface(self) -> ft.Container:
        """Create the main service orders interface"""
        
        # Header with stats and controls
        header = self._create_header()
        
        # View toggle and filters
        controls = self._create_controls()
        
        # Main content area (Kanban or List view)
        content_area = self._create_content_area()
        
        # Main container
        self.container = ft.Container(
            content=ft.Column([
                header,
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                controls,
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                content_area
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=15,
            animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT)
        )
        
        return self.container
    
    def _create_header(self) -> ft.Container:
        """Create header with service order stats"""
        
        # Calculate stats
        total_orders = len(self.sample_orders)
        in_progress = len([o for o in self.sample_orders if o["status"] == "in_progress"])
        scheduled_today = len([o for o in self.sample_orders if o["scheduled_date"] and o["scheduled_date"].date() == datetime.now().date()])
        high_priority = len([o for o in self.sample_orders if o["priority"] == "high"])
        total_value = sum(o["value"] for o in self.sample_orders)
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Column([
                        ft.Text(
                            "🔧 Zlecenia Serwisowe",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.colors.PRIMARY
                        ),
                        ft.Text(
                            "Zarządzanie Przepływem Pracy HVAC",
                            size=14,
                            color=ft.colors.ON_SURFACE_VARIANT
                        )
                    ], spacing=5),
                    ft.Container(expand=True),
                    ft.Row([
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.ADD_TASK),
                                ft.Text("Nowe Zlecenie")
                            ], spacing=8),
                            on_click=self._create_new_order
                        ),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.SMART_TOY),
                                ft.Text("AI Dyspozycja")
                            ], spacing=8),
                            on_click=self._run_ai_dispatch
                        )
                    ], spacing=10)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                
                # Stats row
                ft.Row([
                    self._create_stat_card("Wszystkie Zlecenia", str(total_orders), ft.icons.ASSIGNMENT, ft.colors.BLUE),
                    self._create_stat_card("W Trakcie", str(in_progress), ft.icons.PLAY_CIRCLE, ft.colors.ORANGE),
                    self._create_stat_card("Dzisiaj", str(scheduled_today), ft.icons.TODAY, ft.colors.GREEN),
                    self._create_stat_card("Wysoki Priorytet", str(high_priority), ft.icons.PRIORITY_HIGH, ft.colors.RED),
                    self._create_stat_card("Łączna Wartość", f"{total_value:,.0f} zł", ft.icons.ATTACH_MONEY, ft.colors.PURPLE)
                ], spacing=15, scroll=ft.ScrollMode.AUTO)
            ], spacing=0),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_stat_card(self, title: str, value: str, icon, color) -> ft.Container:
        """Create statistics card"""
        return ft.Container(
            content=ft.Row([
                ft.Icon(icon, color=color, size=20),
                ft.Column([
                    ft.Text(value, size=16, weight=ft.FontWeight.BOLD),
                    ft.Text(title, size=10, color=ft.colors.ON_SURFACE_VARIANT)
                ], spacing=2)
            ], spacing=8),
            width=140,
            padding=ft.padding.all(10),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=8,
            border=ft.border.all(1, ft.colors.OUTLINE_VARIANT)
        )
    
    def _create_controls(self) -> ft.Container:
        """Create view controls and filters"""
        
        return ft.Container(
            content=ft.Row([
                # View toggle
                ft.SegmentedButton(
                    selected={self.view_mode},
                    allow_empty_selection=False,
                    segments=[
                        ft.Segment(
                            value="kanban",
                            label=ft.Text("Kanban"),
                            icon=ft.Icon(ft.icons.VIEW_KANBAN)
                        ),
                        ft.Segment(
                            value="list",
                            label=ft.Text("List"),
                            icon=ft.Icon(ft.icons.VIEW_LIST)
                        )
                    ],
                    on_change=self._change_view_mode
                ),
                
                ft.Container(expand=True),
                
                # Filters
                ft.Row([
                    ft.Dropdown(
                        label="Priority",
                        width=120,
                        options=[
                            ft.dropdown.Option("all", "All"),
                            ft.dropdown.Option("high", "High"),
                            ft.dropdown.Option("normal", "Normal"),
                            ft.dropdown.Option("low", "Low")
                        ],
                        value="all"
                    ),
                    ft.Dropdown(
                        label="Technician",
                        width=150,
                        options=[
                            ft.dropdown.Option("all", "All"),
                            ft.dropdown.Option("tomasz", "Tomasz Nowak"),
                            ft.dropdown.Option("marek", "Marek Kowalczyk"),
                            ft.dropdown.Option("pawel", "Paweł Zieliński")
                        ],
                        value="all"
                    ),
                    ft.IconButton(
                        icon=ft.icons.FILTER_LIST,
                        tooltip="More Filters"
                    )
                ], spacing=10)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12
        )
    
    def _create_content_area(self) -> ft.Container:
        """Create main content area based on view mode"""
        
        if self.view_mode == "kanban":
            return self._create_kanban_view()
        else:
            return self._create_list_view()
    
    def _create_kanban_view(self) -> ft.Container:
        """Create Kanban board view"""
        
        # Create columns for each stage
        kanban_columns = []
        
        for stage in self.kanban_stages:
            # Get orders for this stage
            stage_orders = [o for o in self.sample_orders if o["status"] == stage["id"]]
            
            # Create order cards
            order_cards = []
            for order in stage_orders:
                card = self._create_order_card(order)
                order_cards.append(card)
            
            # Create stage column
            column = ft.Container(
                content=ft.Column([
                    # Stage header z ikoną
                    ft.Container(
                        content=ft.Row([
                            ft.Icon(stage["icon"], color=ft.colors.WHITE, size=16),
                            ft.Text(
                                stage["name"],
                                size=14,
                                weight=ft.FontWeight.BOLD,
                                color=ft.colors.WHITE
                            ),
                            ft.Container(expand=True),
                            ft.Container(
                                content=ft.Text(
                                    str(len(stage_orders)),
                                    size=12,
                                    color=ft.colors.WHITE,
                                    weight=ft.FontWeight.BOLD
                                ),
                                bgcolor=ft.colors.with_opacity(0.3, ft.colors.WHITE),
                                padding=ft.padding.symmetric(horizontal=6, vertical=2),
                                border_radius=10
                            )
                        ], spacing=8),
                        bgcolor=stage["color"],
                        padding=ft.padding.all(12),
                        border_radius=ft.border_radius.only(top_left=8, top_right=8)
                    ),
                    
                    # Order cards
                    ft.Container(
                        content=ft.Column(
                            order_cards,
                            spacing=8,
                            scroll=ft.ScrollMode.AUTO
                        ),
                        padding=ft.padding.all(8),
                        bgcolor=ft.colors.SURFACE_VARIANT,
                        border_radius=ft.border_radius.only(bottom_left=8, bottom_right=8),
                        height=500
                    )
                ], spacing=0),
                width=280,
                shadow=ft.BoxShadow(
                    spread_radius=1,
                    blur_radius=4,
                    color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                    offset=ft.Offset(0, 2)
                )
            )
            
            kanban_columns.append(column)
        
        return ft.Container(
            content=ft.Row(
                kanban_columns,
                spacing=15,
                scroll=ft.ScrollMode.AUTO,
                alignment=ft.MainAxisAlignment.START
            ),
            padding=ft.padding.all(10),
            bgcolor=ft.colors.SURFACE,
            border_radius=12
        )
    
    def _create_order_card(self, order: Dict) -> ft.Container:
        """Create service order card for Kanban board"""
        
        # Priority color
        priority_color = {
            "high": ft.colors.RED,
            "normal": ft.colors.BLUE,
            "low": ft.colors.GREEN
        }.get(order["priority"], ft.colors.GREY)
        
        # Format scheduled date
        scheduled_text = "Not scheduled"
        if order["scheduled_date"]:
            if order["scheduled_date"].date() == datetime.now().date():
                scheduled_text = "Today"
            elif order["scheduled_date"].date() == (datetime.now() + timedelta(days=1)).date():
                scheduled_text = "Tomorrow"
            else:
                scheduled_text = order["scheduled_date"].strftime("%m/%d")
        
        card = ft.Container(
            content=ft.Column([
                # Header with ID and priority
                ft.Row([
                    ft.Text(
                        order["id"],
                        size=11,
                        weight=ft.FontWeight.BOLD,
                        color=ft.colors.PRIMARY
                    ),
                    ft.Container(expand=True),
                    ft.Container(
                        content=ft.Text(
                            order["priority"].upper(),
                            size=8,
                            color=ft.colors.WHITE,
                            weight=ft.FontWeight.BOLD
                        ),
                        bgcolor=priority_color,
                        padding=ft.padding.symmetric(horizontal=4, vertical=2),
                        border_radius=6
                    )
                ]),
                
                # Title and customer
                ft.Text(
                    order["title"],
                    size=12,
                    weight=ft.FontWeight.W_500,
                    max_lines=2,
                    overflow=ft.TextOverflow.ELLIPSIS
                ),
                ft.Text(
                    order["customer_name"],
                    size=10,
                    color=ft.colors.ON_SURFACE_VARIANT
                ),
                
                # Kategoria HVAC
                ft.Container(
                    content=ft.Text(
                        order["category"].replace("_", " ").title(),
                        size=8,
                        color=ft.colors.WHITE,
                        weight=ft.FontWeight.BOLD
                    ),
                    bgcolor={
                        "serwis": ft.colors.BLUE,
                        "nowa_instalacja": ft.colors.GREEN,
                        "ogledziny": ft.colors.ORANGE
                    }.get(order["category"], ft.colors.GREY),
                    padding=ft.padding.symmetric(horizontal=4, vertical=2),
                    border_radius=6
                ),

                # Equipment and value
                ft.Row([
                    ft.Icon(ft.icons.SETTINGS, size=12, color=ft.colors.ON_SURFACE_VARIANT),
                    ft.Text(order["equipment_type"], size=10, expand=True, overflow=ft.TextOverflow.ELLIPSIS),
                ], spacing=4),

                # Czynnik chłodniczy i gwarancja
                ft.Row([
                    ft.Icon(ft.icons.SCIENCE, size=10, color=ft.colors.CYAN),
                    ft.Text(order["refrigerant"], size=9),
                    ft.Container(expand=True),
                    ft.Icon(ft.icons.SHIELD, size=10, color=ft.colors.GREEN if order["warranty_status"] == "w_gwarancji" else ft.colors.ORANGE),
                    ft.Text(order["warranty_status"].replace("_", " "), size=9)
                ], spacing=4),

                ft.Row([
                    ft.Icon(ft.icons.ATTACH_MONEY, size=12, color=ft.colors.GREEN),
                    ft.Text(f"{order['value']:,.0f} zł", size=10, weight=ft.FontWeight.BOLD),
                    ft.Container(expand=True),
                    ft.Icon(ft.icons.SCHEDULE, size=12, color=ft.colors.ON_SURFACE_VARIANT),
                    ft.Text(scheduled_text, size=10)
                ], spacing=4),
                
                # Technician
                ft.Row([
                    ft.Icon(ft.icons.PERSON, size=12, color=ft.colors.ON_SURFACE_VARIANT),
                    ft.Text(
                        order["assigned_technician"] or "Unassigned",
                        size=10,
                        color=ft.colors.ON_SURFACE_VARIANT if order["assigned_technician"] else ft.colors.ERROR
                    )
                ], spacing=4) if order.get("assigned_technician") or order["status"] != "backlog" else ft.Container()
            ], spacing=6),
            padding=ft.padding.all(10),
            bgcolor=ft.colors.SURFACE,
            border_radius=8,
            border=ft.border.all(1, ft.colors.OUTLINE_VARIANT),
            animate=ft.animation.Animation(200, ft.AnimationCurve.EASE_OUT),
            on_click=lambda e, order=order: self._select_order(order),
            ink=True
        )
        
        return card
    
    def _create_list_view(self) -> ft.Container:
        """Create list view of service orders"""
        
        # Table headers
        headers = ft.Row([
            ft.Text("Order ID", size=12, weight=ft.FontWeight.BOLD, width=100),
            ft.Text("Customer", size=12, weight=ft.FontWeight.BOLD, width=150),
            ft.Text("Title", size=12, weight=ft.FontWeight.BOLD, expand=True),
            ft.Text("Status", size=12, weight=ft.FontWeight.BOLD, width=120),
            ft.Text("Technician", size=12, weight=ft.FontWeight.BOLD, width=120),
            ft.Text("Scheduled", size=12, weight=ft.FontWeight.BOLD, width=100),
            ft.Text("Value", size=12, weight=ft.FontWeight.BOLD, width=80),
            ft.Text("Actions", size=12, weight=ft.FontWeight.BOLD, width=80)
        ], spacing=10)
        
        # Order rows
        order_rows = []
        for order in self.sample_orders:
            row = self._create_order_row(order)
            order_rows.append(row)
        
        return ft.Container(
            content=ft.Column([
                headers,
                ft.Divider(),
                ft.Column(
                    order_rows,
                    spacing=5,
                    scroll=ft.ScrollMode.AUTO
                )
            ], spacing=10),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            height=500
        )
    
    def _create_order_row(self, order: Dict) -> ft.Container:
        """Create order row for list view"""
        
        # Status color
        status_color = next((s["color"] for s in self.kanban_stages if s["id"] == order["status"]), ft.colors.GREY)
        
        # Format scheduled date
        scheduled_text = "Not scheduled"
        if order["scheduled_date"]:
            scheduled_text = order["scheduled_date"].strftime("%m/%d")
        
        return ft.Container(
            content=ft.Row([
                ft.Text(order["id"], size=11, width=100),
                ft.Text(order["customer_name"], size=11, width=150, overflow=ft.TextOverflow.ELLIPSIS),
                ft.Text(order["title"], size=11, expand=True, overflow=ft.TextOverflow.ELLIPSIS),
                ft.Container(
                    content=ft.Text(
                        order["status"].replace("_", " ").title(),
                        size=10,
                        color=ft.colors.WHITE,
                        weight=ft.FontWeight.BOLD
                    ),
                    bgcolor=status_color,
                    padding=ft.padding.symmetric(horizontal=6, vertical=2),
                    border_radius=8,
                    width=120
                ),
                ft.Text(order["assigned_technician"] or "Unassigned", size=11, width=120, overflow=ft.TextOverflow.ELLIPSIS),
                ft.Text(scheduled_text, size=11, width=100),
                ft.Text(f"${order['value']:,.0f}", size=11, width=80),
                ft.IconButton(
                    icon=ft.icons.MORE_VERT,
                    icon_size=16,
                    width=80,
                    on_click=lambda e, order=order: self._show_order_menu(order)
                )
            ], spacing=10),
            padding=ft.padding.symmetric(vertical=8, horizontal=5),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=4,
            on_click=lambda e, order=order: self._select_order(order),
            ink=True
        )
    
    def _change_view_mode(self, e):
        """Change view mode between Kanban and List"""
        self.view_mode = e.control.selected.pop()
        logger.info(f"View mode changed to: {self.view_mode}")
        if self.page:
            self.page.update()
    
    def _select_order(self, order: Dict):
        """Select service order"""
        self.selected_order = order
        logger.info(f"Selected order: {order['id']}")
        # TODO: Show order details panel
    
    def _create_new_order(self, e):
        """Create new service order"""
        logger.info("Create new order clicked")
        # TODO: Implement new order dialog
    
    def _run_ai_dispatch(self, e):
        """Run AI-powered dispatch optimization"""
        logger.info("Running AI dispatch...")
        # TODO: Implement AI dispatch logic
    
    def _show_order_menu(self, order: Dict):
        """Show order context menu"""
        logger.info(f"Show menu for order: {order['id']}")
        # TODO: Implement context menu
