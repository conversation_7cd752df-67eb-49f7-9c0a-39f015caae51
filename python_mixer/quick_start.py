#!/usr/bin/env python3
"""
🚀 QUICK START - COMPLETE TRANSCRIPTION PIPELINE
===============================================

Quick start script for running the complete transcription pipeline workflow.
This script provides an easy way to test and validate the entire system.

Usage:
    python quick_start.py

Features:
- Automatic environment validation
- Service health checks
- Quick pipeline test
- User-friendly output

Author: Enhanced Python Mixer System
Date: 2025-05-30
Version: 1.0.0 - Production Ready
"""

import asyncio
import sys
from pathlib import Path
from rich.console import Console
from rich.panel import Panel
from rich.prompt import Confirm, IntPrompt
from rich.text import Text

# Add project root to path
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from run_complete_pipeline_test import CompletePipelineTestRunner


class QuickStartRunner:
    """Quick start runner for the complete transcription pipeline."""
    
    def __init__(self):
        self.console = Console()
        
    def display_welcome(self):
        """Display welcome message and system overview."""
        welcome_text = Text()
        welcome_text.append("🎯 COMPLETE TRANSCRIPTION PIPELINE\n", style="bold magenta")
        welcome_text.append("HVAC CRM System - Quick Start\n\n", style="bold cyan")
        
        welcome_text.append("This system provides:\n", style="bold white")
        welcome_text.append("• Email <NAME_EMAIL>\n", style="green")
        welcome_text.append("• M4A audio transcription with NVIDIA NeMo STT\n", style="green")
        welcome_text.append("• 5 HVAC agent types for semantic analysis\n", style="green")
        welcome_text.append("• Unified 360-degree customer profiles\n", style="green")
        welcome_text.append("• GoSpine API and Django CRM integration\n", style="green")
        welcome_text.append("• Comprehensive validation and monitoring\n\n", style="green")
        
        welcome_text.append("Ready to test the complete workflow!\n", style="bold yellow")
        
        welcome_panel = Panel(
            welcome_text,
            border_style="magenta",
            padding=(1, 2)
        )
        self.console.print(welcome_panel)
        
    def get_user_preferences(self) -> dict:
        """Get user preferences for the test run."""
        self.console.print("\n[bold cyan]Configuration Options[/bold cyan]")
        
        # Ask for test mode
        validation_only = Confirm.ask(
            "Run validation tests only? (Skip pipeline execution)",
            default=False
        )
        
        if not validation_only:
            # Ask for number of files
            max_files = IntPrompt.ask(
                "How many M4A files to process?",
                default=20,
                show_default=True
            )
        else:
            max_files = 0
            
        return {
            'validation_only': validation_only,
            'max_files': max_files
        }
        
    async def run_quick_test(self, preferences: dict):
        """Run the quick test with user preferences."""
        self.console.print("\n[bold green]🚀 Starting Pipeline Test...[/bold green]")
        
        # Create test runner
        test_runner = CompletePipelineTestRunner(
            max_files=preferences['max_files'],
            validation_only=preferences['validation_only']
        )
        
        # Run the test
        results = await test_runner.run_complete_test()
        
        return results
        
    def display_quick_summary(self, results: dict):
        """Display a quick summary of results."""
        overall_status = results.get('overall_status', 'UNKNOWN')
        
        if overall_status == 'SUCCESS':
            status_color = "green"
            status_icon = "✅"
            status_message = "All tests passed successfully!"
        elif overall_status == 'PARTIAL_SUCCESS':
            status_color = "yellow"
            status_icon = "⚠️"
            status_message = "Tests completed with some issues"
        else:
            status_color = "red"
            status_icon = "❌"
            status_message = "Tests failed - check logs for details"
            
        summary_text = Text()
        summary_text.append(f"{status_icon} TEST RESULTS\n\n", style=f"bold {status_color}")
        summary_text.append(f"Status: {overall_status}\n", style=status_color)
        summary_text.append(f"Message: {status_message}\n\n", style=status_color)
        
        # Add specific results if available
        if 'pipeline_results' in results:
            pipeline_stats = results['pipeline_results'].get('statistics', {})
            if pipeline_stats:
                summary_text.append("Pipeline Results:\n", style="bold cyan")
                summary_text.append(f"• Success Rate: {pipeline_stats.get('success_rate', 0):.1f}%\n")
                summary_text.append(f"• Duration: {pipeline_stats.get('total_duration_formatted', 'N/A')}\n")
                summary_text.append(f"• Phases: {pipeline_stats.get('phases_completed', 0)}/5\n\n")
                
        if 'validation_results' in results:
            val_summary = results['validation_results'].get('summary', {})
            if val_summary:
                summary_text.append("Validation Results:\n", style="bold cyan")
                summary_text.append(f"• Tests Passed: {val_summary.get('passed_tests', 0)}/{val_summary.get('total_tests', 0)}\n")
                summary_text.append(f"• Success Rate: {val_summary.get('success_rate', 0):.1f}%\n\n")
                
        summary_text.append("📄 Detailed reports saved in project directory\n", style="dim")
        summary_text.append("📋 Check logs/ directory for detailed information", style="dim")
        
        summary_panel = Panel(
            summary_text,
            border_style=status_color,
            padding=(1, 2)
        )
        self.console.print(summary_panel)
        
    async def run(self):
        """Run the complete quick start workflow."""
        try:
            # Display welcome
            self.display_welcome()
            
            # Get user preferences
            preferences = self.get_user_preferences()
            
            # Confirm execution
            if preferences['validation_only']:
                confirm_message = "Run validation tests only?"
            else:
                confirm_message = f"Process {preferences['max_files']} M4A files and run full pipeline?"
                
            if not Confirm.ask(confirm_message, default=True):
                self.console.print("[yellow]Operation cancelled by user[/yellow]")
                return
                
            # Run the test
            results = await self.run_quick_test(preferences)
            
            # Display summary
            self.display_quick_summary(results)
            
            # Ask about next steps
            self.console.print("\n[bold cyan]Next Steps:[/bold cyan]")
            if results.get('overall_status') == 'SUCCESS':
                self.console.print("• System is ready for production use")
                self.console.print("• Consider running with more files for stress testing")
                self.console.print("• Review generated customer profiles in the CRM")
            else:
                self.console.print("• Check detailed logs for error analysis")
                self.console.print("• Verify all services are running correctly")
                self.console.print("• Run validation-only mode to isolate issues")
                
        except KeyboardInterrupt:
            self.console.print("\n[yellow]Operation cancelled by user[/yellow]")
        except Exception as e:
            self.console.print(f"\n[red]Error during execution: {e}[/red]")
            self.console.print("[dim]Check logs for detailed error information[/dim]")


async def main():
    """Main execution function."""
    runner = QuickStartRunner()
    await runner.run()


if __name__ == "__main__":
    asyncio.run(main())
