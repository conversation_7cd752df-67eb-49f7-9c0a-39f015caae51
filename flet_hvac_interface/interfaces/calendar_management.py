"""
📅 Kalendarz HVAC - Zarządzanie Harmonogramem
Zaawansowany kalendarz z 3 kategoriami: <PERSON><PERSON><PERSON>, Nowa Instalacja, Oględziny
"""

import flet as ft
import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import json
import calendar

logger = logging.getLogger(__name__)

class CalendarManagementInterface:
    """
    Interfejs zarządzania kalendarzem HVAC z 3 kategoriami działalności
    Funkcje: planowanie wizyt, optymalizacja tras, zarządzanie zespołami
    """
    
    def __init__(self, page: ft.Page, threading_manager=None, semantic_framework=None):
        self.page = page
        self.threading_manager = threading_manager
        self.semantic_framework = semantic_framework
        self.container = None
        self.current_date = datetime.now()
        self.selected_date = datetime.now()
        self.view_mode = "month"  # month, week, day
        self.selected_category = "wszystkie"  # wszystkie, serwis, nowa_instalacja, ogledziny
        
        # Kategorie HVAC
        self.hvac_categories = {
            "serwis": {
                "name": "<PERSON>wi<PERSON>",
                "color": ft.colors.BLUE,
                "icon": ft.icons.BUILD,
                "description": "Przeglądy, naprawy, konserwacja"
            },
            "nowa_instalacja": {
                "name": "Nowa Instalacja", 
                "color": ft.colors.GREEN,
                "icon": ft.icons.CONSTRUCTION,
                "description": "Montaż nowych systemów HVAC"
            },
            "ogledziny": {
                "name": "Oględziny",
                "color": ft.colors.ORANGE,
                "icon": ft.icons.SEARCH,
                "description": "Wyceny, audyty, inspekcje"
            }
        }
        
        # Przykładowe wydarzenia w kalendarzu
        self.sample_events = [
            {
                "id": "E-2024-001",
                "title": "Serwis klimatyzacji - Biurowiec",
                "category": "serwis",
                "customer": "Jan Kowalski",
                "technician": "Tomasz Nowak",
                "date": datetime.now() + timedelta(days=1),
                "start_time": "09:00",
                "end_time": "13:00",
                "location": "ul. Marszałkowska 1, Warszawa",
                "equipment": "Daikin VRV IV-S",
                "priority": "normal",
                "status": "zaplanowane",
                "notes": "Przegląd okresowy z wymianą filtrów"
            },
            {
                "id": "E-2024-002",
                "title": "Montaż systemu Multi V",
                "category": "nowa_instalacja",
                "customer": "Firma ABC Sp. z o.o.",
                "technician": "Paweł Zieliński",
                "date": datetime.now() + timedelta(days=2),
                "start_time": "08:00",
                "end_time": "16:00",
                "location": "ul. Puławska 120, Warszawa",
                "equipment": "LG Multi V 5",
                "priority": "high",
                "status": "zaplanowane",
                "notes": "Instalacja w 3 pomieszczeniach biurowych"
            },
            {
                "id": "E-2024-003",
                "title": "Oględziny przed modernizacją",
                "category": "ogledziny",
                "customer": "Hotel Centrum",
                "technician": "Krzysztof Wiśniewski",
                "date": datetime.now() + timedelta(days=3),
                "start_time": "10:00",
                "end_time": "12:00",
                "location": "ul. Krakowskie Przedmieście 5, Warszawa",
                "equipment": "Stary system split",
                "priority": "normal",
                "status": "zaplanowane",
                "notes": "Ocena stanu przed wymianą na VRV"
            },
            {
                "id": "E-2024-004",
                "title": "Awaryjna naprawa sprężarki",
                "category": "serwis",
                "customer": "Restauracja Smaki",
                "technician": "Marek Kowalczyk",
                "date": datetime.now(),
                "start_time": "14:00",
                "end_time": "17:00",
                "location": "ul. Nowy Świat 15, Warszawa",
                "equipment": "LG Multi V 5",
                "priority": "urgent",
                "status": "w_trakcie",
                "notes": "Wymiana sprężarki - awaria"
            },
            {
                "id": "E-2024-005",
                "title": "Instalacja systemu VRV",
                "category": "nowa_instalacja",
                "customer": "Biuro Rachunkowe JK",
                "technician": "Tomasz Nowak",
                "date": datetime.now() + timedelta(days=5),
                "start_time": "07:30",
                "end_time": "15:30",
                "location": "ul. Żurawia 10, Warszawa",
                "equipment": "Daikin VRV IV-S",
                "priority": "normal",
                "status": "zaplanowane",
                "notes": "Kompletna instalacja z montażem jednostek wewnętrznych"
            }
        ]
        
        logger.info("📅 Calendar Management Interface initialized")
    
    def create_interface(self) -> ft.Container:
        """Utwórz główny interfejs kalendarza"""
        
        # Nagłówek z kontrolkami
        header = self._create_header()
        
        # Kontrolki widoku i filtrów
        controls = self._create_controls()
        
        # Główny obszar kalendarza
        calendar_area = self._create_calendar_area()
        
        # Główny kontener
        self.container = ft.Container(
            content=ft.Column([
                header,
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                controls,
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                calendar_area
            ], spacing=0, scroll=ft.ScrollMode.AUTO),
            padding=ft.padding.all(20),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=15,
            animate=ft.animation.Animation(300, ft.AnimationCurve.EASE_OUT)
        )
        
        return self.container
    
    def _create_header(self) -> ft.Container:
        """Utwórz nagłówek kalendarza"""
        
        # Statystyki dzisiejszych wydarzeń
        today_events = [e for e in self.sample_events if e["date"].date() == datetime.now().date()]
        serwis_count = len([e for e in today_events if e["category"] == "serwis"])
        instalacja_count = len([e for e in today_events if e["category"] == "nowa_instalacja"])
        ogledziny_count = len([e for e in today_events if e["category"] == "ogledziny"])
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Column([
                        ft.Text(
                            "📅 Kalendarz HVAC",
                            size=24,
                            weight=ft.FontWeight.BOLD,
                            color=ft.colors.PRIMARY
                        ),
                        ft.Text(
                            "Zarządzanie Harmonogramem Pracy",
                            size=14,
                            color=ft.colors.ON_SURFACE_VARIANT
                        )
                    ], spacing=5),
                    ft.Container(expand=True),
                    ft.Row([
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.ADD_CIRCLE),
                                ft.Text("Nowe Wydarzenie")
                            ], spacing=8),
                            on_click=self._create_new_event
                        ),
                        ft.ElevatedButton(
                            content=ft.Row([
                                ft.Icon(ft.icons.ROUTE),
                                ft.Text("Optymalizuj Trasy")
                            ], spacing=8),
                            on_click=self._optimize_routes
                        )
                    ], spacing=10)
                ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
                
                ft.Divider(height=15, color=ft.colors.TRANSPARENT),
                
                # Statystyki dzisiejszych wydarzeń
                ft.Row([
                    self._create_category_stat_card("Serwis Dzisiaj", str(serwis_count), ft.icons.BUILD, ft.colors.BLUE),
                    self._create_category_stat_card("Instalacje Dzisiaj", str(instalacja_count), ft.icons.CONSTRUCTION, ft.colors.GREEN),
                    self._create_category_stat_card("Oględziny Dzisiaj", str(ogledziny_count), ft.icons.SEARCH, ft.colors.ORANGE),
                    self._create_category_stat_card("Łącznie Dzisiaj", str(len(today_events)), ft.icons.TODAY, ft.colors.PURPLE)
                ], spacing=15, scroll=ft.ScrollMode.AUTO)
            ], spacing=0),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            shadow=ft.BoxShadow(
                spread_radius=1,
                blur_radius=8,
                color=ft.colors.with_opacity(0.1, ft.colors.BLACK),
                offset=ft.Offset(0, 2)
            )
        )
    
    def _create_category_stat_card(self, title: str, value: str, icon, color) -> ft.Container:
        """Utwórz kartę statystyk kategorii"""
        return ft.Container(
            content=ft.Row([
                ft.Icon(icon, color=color, size=20),
                ft.Column([
                    ft.Text(value, size=16, weight=ft.FontWeight.BOLD),
                    ft.Text(title, size=10, color=ft.colors.ON_SURFACE_VARIANT)
                ], spacing=2)
            ], spacing=8),
            width=140,
            padding=ft.padding.all(10),
            bgcolor=ft.colors.SURFACE_VARIANT,
            border_radius=8,
            border=ft.border.all(1, ft.colors.OUTLINE_VARIANT)
        )
    
    def _create_controls(self) -> ft.Container:
        """Utwórz kontrolki widoku i filtrów"""
        
        return ft.Container(
            content=ft.Row([
                # Przełącznik widoku
                ft.SegmentedButton(
                    selected={self.view_mode},
                    allow_empty_selection=False,
                    segments=[
                        ft.Segment(
                            value="day",
                            label=ft.Text("Dzień"),
                            icon=ft.Icon(ft.icons.TODAY)
                        ),
                        ft.Segment(
                            value="week",
                            label=ft.Text("Tydzień"),
                            icon=ft.Icon(ft.icons.VIEW_WEEK)
                        ),
                        ft.Segment(
                            value="month",
                            label=ft.Text("Miesiąc"),
                            icon=ft.Icon(ft.icons.CALENDAR_MONTH)
                        )
                    ],
                    on_change=self._change_view_mode
                ),
                
                ft.Container(expand=True),
                
                # Filtry kategorii
                ft.Row([
                    ft.Dropdown(
                        label="Kategoria",
                        width=150,
                        options=[
                            ft.dropdown.Option("wszystkie", "Wszystkie"),
                            ft.dropdown.Option("serwis", "🔧 Serwis"),
                            ft.dropdown.Option("nowa_instalacja", "🏗️ Nowa Instalacja"),
                            ft.dropdown.Option("ogledziny", "🔍 Oględziny")
                        ],
                        value=self.selected_category,
                        on_change=self._filter_by_category
                    ),
                    ft.Dropdown(
                        label="Technik",
                        width=150,
                        options=[
                            ft.dropdown.Option("wszyscy", "Wszyscy"),
                            ft.dropdown.Option("tomasz", "Tomasz Nowak"),
                            ft.dropdown.Option("marek", "Marek Kowalczyk"),
                            ft.dropdown.Option("pawel", "Paweł Zieliński"),
                            ft.dropdown.Option("krzysztof", "Krzysztof Wiśniewski")
                        ],
                        value="wszyscy"
                    ),
                    ft.IconButton(
                        icon=ft.icons.REFRESH,
                        tooltip="Odśwież kalendarz",
                        on_click=self._refresh_calendar
                    )
                ], spacing=10)
            ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12
        )
    
    def _create_calendar_area(self) -> ft.Container:
        """Utwórz główny obszar kalendarza"""
        
        if self.view_mode == "month":
            return self._create_month_view()
        elif self.view_mode == "week":
            return self._create_week_view()
        else:
            return self._create_day_view()
    
    def _create_month_view(self) -> ft.Container:
        """Utwórz widok miesięczny"""
        
        # Nagłówek miesiąca
        month_header = ft.Row([
            ft.IconButton(
                icon=ft.icons.CHEVRON_LEFT,
                on_click=self._previous_month
            ),
            ft.Text(
                f"{calendar.month_name[self.current_date.month]} {self.current_date.year}",
                size=20,
                weight=ft.FontWeight.BOLD,
                expand=True,
                text_align=ft.TextAlign.CENTER
            ),
            ft.IconButton(
                icon=ft.icons.CHEVRON_RIGHT,
                on_click=self._next_month
            )
        ])
        
        # Dni tygodnia
        weekdays = ft.Row([
            ft.Container(
                content=ft.Text(day, weight=ft.FontWeight.BOLD, text_align=ft.TextAlign.CENTER),
                expand=True,
                padding=ft.padding.all(10)
            )
            for day in ["Pon", "Wt", "Śr", "Czw", "Pt", "Sob", "Nie"]
        ])
        
        # Siatka kalendarza
        calendar_grid = self._create_calendar_grid()
        
        return ft.Container(
            content=ft.Column([
                month_header,
                ft.Divider(),
                weekdays,
                calendar_grid
            ], spacing=10),
            padding=ft.padding.all(15),
            bgcolor=ft.colors.SURFACE,
            border_radius=12,
            height=600
        )
    
    def _create_calendar_grid(self) -> ft.Column:
        """Utwórz siatkę kalendarza miesięcznego"""
        
        # Pobierz pierwszy dzień miesiąca i liczbę dni
        first_day = self.current_date.replace(day=1)
        last_day = (first_day.replace(month=first_day.month + 1) - timedelta(days=1)).day
        start_weekday = first_day.weekday()  # 0 = poniedziałek
        
        # Utwórz wiersze kalendarza
        rows = []
        current_day = 1
        
        for week in range(6):  # Maksymalnie 6 tygodni
            week_row = []
            
            for day_of_week in range(7):
                if week == 0 and day_of_week < start_weekday:
                    # Puste komórki przed pierwszym dniem miesiąca
                    week_row.append(ft.Container(expand=True, height=80))
                elif current_day > last_day:
                    # Puste komórki po ostatnim dniu miesiąca
                    week_row.append(ft.Container(expand=True, height=80))
                else:
                    # Komórka z dniem
                    day_cell = self._create_day_cell(current_day)
                    week_row.append(day_cell)
                    current_day += 1
            
            if any(isinstance(cell, ft.Container) and cell.content for cell in week_row):
                rows.append(ft.Row(week_row, spacing=2))
            
            if current_day > last_day:
                break
        
        return ft.Column(rows, spacing=2)
    
    def _create_day_cell(self, day: int) -> ft.Container:
        """Utwórz komórkę dnia w kalendarzu"""
        
        # Sprawdź czy to dzisiaj
        cell_date = self.current_date.replace(day=day)
        is_today = cell_date.date() == datetime.now().date()
        
        # Pobierz wydarzenia dla tego dnia
        day_events = [e for e in self.sample_events if e["date"].date() == cell_date.date()]
        
        # Filtruj według wybranej kategorii
        if self.selected_category != "wszystkie":
            day_events = [e for e in day_events if e["category"] == self.selected_category]
        
        # Utwórz wskaźniki wydarzeń
        event_indicators = []
        for event in day_events[:3]:  # Maksymalnie 3 wskaźniki
            color = self.hvac_categories[event["category"]]["color"]
            indicator = ft.Container(
                width=8,
                height=8,
                bgcolor=color,
                border_radius=4,
                tooltip=f"{event['title']} - {event['start_time']}"
            )
            event_indicators.append(indicator)
        
        # Dodaj wskaźnik "więcej" jeśli jest więcej wydarzeń
        if len(day_events) > 3:
            event_indicators.append(
                ft.Container(
                    content=ft.Text(f"+{len(day_events) - 3}", size=8),
                    width=12,
                    height=8,
                    bgcolor=ft.colors.GREY,
                    border_radius=4
                )
            )
        
        return ft.Container(
            content=ft.Column([
                ft.Text(
                    str(day),
                    size=14,
                    weight=ft.FontWeight.BOLD if is_today else ft.FontWeight.NORMAL,
                    color=ft.colors.PRIMARY if is_today else ft.colors.ON_SURFACE
                ),
                ft.Row(
                    event_indicators,
                    spacing=2,
                    wrap=True
                ) if event_indicators else ft.Container()
            ], spacing=5),
            expand=True,
            height=80,
            padding=ft.padding.all(5),
            bgcolor=ft.colors.PRIMARY_CONTAINER if is_today else ft.colors.SURFACE_VARIANT,
            border_radius=8,
            border=ft.border.all(1, ft.colors.PRIMARY if is_today else ft.colors.OUTLINE_VARIANT),
            on_click=lambda e, day=day: self._select_day(day),
            ink=True
        )
    
    def _create_week_view(self) -> ft.Container:
        """Utwórz widok tygodniowy"""
        return ft.Container(
            content=ft.Text("🗓️ Widok tygodniowy - w przygotowaniu", size=16),
            padding=ft.padding.all(20),
            alignment=ft.alignment.center,
            height=400
        )
    
    def _create_day_view(self) -> ft.Container:
        """Utwórz widok dzienny"""
        return ft.Container(
            content=ft.Text("📅 Widok dzienny - w przygotowaniu", size=16),
            padding=ft.padding.all(20),
            alignment=ft.alignment.center,
            height=400
        )
    
    def _change_view_mode(self, e):
        """Zmień tryb widoku kalendarza"""
        self.view_mode = e.control.selected.pop()
        logger.info(f"Zmieniono widok kalendarza na: {self.view_mode}")
        if self.page:
            self.page.update()
    
    def _filter_by_category(self, e):
        """Filtruj wydarzenia według kategorii"""
        self.selected_category = e.control.value
        logger.info(f"Filtrowanie według kategorii: {self.selected_category}")
        if self.page:
            self.page.update()
    
    def _select_day(self, day: int):
        """Wybierz dzień w kalendarzu"""
        self.selected_date = self.current_date.replace(day=day)
        logger.info(f"Wybrano dzień: {self.selected_date.strftime('%Y-%m-%d')}")
        # TODO: Pokaż szczegóły dnia
    
    def _previous_month(self, e):
        """Przejdź do poprzedniego miesiąca"""
        if self.current_date.month == 1:
            self.current_date = self.current_date.replace(year=self.current_date.year - 1, month=12)
        else:
            self.current_date = self.current_date.replace(month=self.current_date.month - 1)
        if self.page:
            self.page.update()
    
    def _next_month(self, e):
        """Przejdź do następnego miesiąca"""
        if self.current_date.month == 12:
            self.current_date = self.current_date.replace(year=self.current_date.year + 1, month=1)
        else:
            self.current_date = self.current_date.replace(month=self.current_date.month + 1)
        if self.page:
            self.page.update()
    
    def _create_new_event(self, e):
        """Utwórz nowe wydarzenie"""
        logger.info("Tworzenie nowego wydarzenia")
        # TODO: Implementuj dialog tworzenia wydarzenia
    
    def _optimize_routes(self, e):
        """Optymalizuj trasy techników"""
        logger.info("Optymalizacja tras")
        # TODO: Implementuj optymalizację tras
    
    def _refresh_calendar(self, e):
        """Odśwież kalendarz"""
        logger.info("Odświeżanie kalendarza...")
        if self.page:
            self.page.update()
