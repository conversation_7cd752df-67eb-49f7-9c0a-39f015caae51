#!/usr/bin/env python3
"""
🔄 ENHANCED QUEUE MANAGER
========================

Advanced Redis-based queue management system for the HVAC CRM transcription pipeline.
Provides scalable, persistent, and fault-tolerant queue operations with comprehensive
monitoring and analytics capabilities.

Features:
- Redis-based persistent queues
- Priority queue support
- Dead letter queue handling
- Queue monitoring and analytics
- Batch processing capabilities
- Retry mechanisms with exponential backoff

Author: Enhanced Python Mixer System
Date: 2025-05-30
Version: 1.0.0 - Production Ready
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass, asdict
from enum import Enum

import redis
from redis.exceptions import RedisError


class QueuePriority(Enum):
    """Queue priority levels."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class QueueItem:
    """Queue item data structure."""
    id: str
    data: Dict[str, Any]
    priority: QueuePriority
    created_at: str
    retry_count: int = 0
    max_retries: int = 3
    processing_timeout: int = 300  # 5 minutes
    metadata: Optional[Dict[str, Any]] = None


class EnhancedQueueManager:
    """Enhanced Redis-based queue manager with advanced features."""
    
    def __init__(self, redis_client: redis.Redis, namespace: str = "hvac_crm"):
        """
        Initialize the enhanced queue manager.
        
        Args:
            redis_client: Redis client instance
            namespace: Namespace for queue keys
        """
        self.redis = redis_client
        self.namespace = namespace
        self.logger = logging.getLogger(__name__)
        
        # Queue key patterns
        self.queue_key = f"{namespace}:queue:{{queue_name}}"
        self.processing_key = f"{namespace}:processing:{{queue_name}}"
        self.dead_letter_key = f"{namespace}:dead_letter:{{queue_name}}"
        self.stats_key = f"{namespace}:stats:{{queue_name}}"
        
        # Initialize queue statistics
        self._initialize_stats()
        
    def _initialize_stats(self):
        """Initialize queue statistics tracking."""
        try:
            # Create basic stats structure if not exists
            stats_keys = [
                f"{self.namespace}:global_stats",
                f"{self.namespace}:queue_registry"
            ]
            
            for key in stats_keys:
                if not self.redis.exists(key):
                    self.redis.hset(key, "initialized", datetime.now().isoformat())
                    
        except RedisError as e:
            self.logger.error(f"Failed to initialize stats: {e}")
            
    async def add_to_queue(self, queue_name: str, item_data: Union[Dict, QueueItem], 
                          priority: QueuePriority = QueuePriority.MEDIUM) -> str:
        """
        Add an item to the specified queue.
        
        Args:
            queue_name: Name of the queue
            item_data: Data to add to queue (dict or QueueItem)
            priority: Priority level for the item
            
        Returns:
            Item ID
        """
        try:
            # Convert to QueueItem if necessary
            if isinstance(item_data, dict):
                item_id = f"{queue_name}_{int(time.time() * 1000)}"
                queue_item = QueueItem(
                    id=item_id,
                    data=item_data,
                    priority=priority,
                    created_at=datetime.now().isoformat()
                )
            else:
                queue_item = item_data
                
            # Serialize the queue item
            serialized_item = json.dumps(asdict(queue_item))
            
            # Add to Redis list with priority handling
            queue_key = self.queue_key.format(queue_name=queue_name)
            
            if priority in [QueuePriority.HIGH, QueuePriority.CRITICAL]:
                # Add to front for high priority
                self.redis.lpush(queue_key, serialized_item)
            else:
                # Add to back for normal priority
                self.redis.rpush(queue_key, serialized_item)
                
            # Update statistics
            await self._update_stats(queue_name, 'items_added', 1)
            await self._register_queue(queue_name)
            
            self.logger.info(f"Added item {queue_item.id} to queue {queue_name}")
            return queue_item.id
            
        except Exception as e:
            self.logger.error(f"Failed to add item to queue {queue_name}: {e}")
            raise
            
    async def get_from_queue(self, queue_name: str, timeout: int = 10) -> Optional[QueueItem]:
        """
        Get an item from the specified queue.
        
        Args:
            queue_name: Name of the queue
            timeout: Timeout in seconds for blocking pop
            
        Returns:
            QueueItem or None if queue is empty
        """
        try:
            queue_key = self.queue_key.format(queue_name=queue_name)
            processing_key = self.processing_key.format(queue_name=queue_name)
            
            # Blocking pop from queue
            result = self.redis.blpop(queue_key, timeout=timeout)
            
            if not result:
                return None
                
            # Deserialize the item
            _, serialized_item = result
            item_dict = json.loads(serialized_item)
            
            # Convert priority back to enum
            if isinstance(item_dict['priority'], int):
                item_dict['priority'] = QueuePriority(item_dict['priority'])
            else:
                item_dict['priority'] = QueuePriority[item_dict['priority']]
                
            queue_item = QueueItem(**item_dict)
            
            # Move to processing queue
            processing_data = {
                'item': serialized_item,
                'started_at': datetime.now().isoformat(),
                'timeout_at': (datetime.now() + timedelta(seconds=queue_item.processing_timeout)).isoformat()
            }
            
            self.redis.hset(processing_key, queue_item.id, json.dumps(processing_data))
            
            # Update statistics
            await self._update_stats(queue_name, 'items_processed', 1)
            
            self.logger.info(f"Retrieved item {queue_item.id} from queue {queue_name}")
            return queue_item
            
        except Exception as e:
            self.logger.error(f"Failed to get item from queue {queue_name}: {e}")
            return None
            
    async def complete_item(self, queue_name: str, item_id: str) -> bool:
        """
        Mark an item as completed and remove from processing queue.
        
        Args:
            queue_name: Name of the queue
            item_id: ID of the completed item
            
        Returns:
            True if successful, False otherwise
        """
        try:
            processing_key = self.processing_key.format(queue_name=queue_name)
            
            # Remove from processing queue
            result = self.redis.hdel(processing_key, item_id)
            
            if result:
                await self._update_stats(queue_name, 'items_completed', 1)
                self.logger.info(f"Completed item {item_id} in queue {queue_name}")
                return True
            else:
                self.logger.warning(f"Item {item_id} not found in processing queue {queue_name}")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to complete item {item_id} in queue {queue_name}: {e}")
            return False
            
    async def retry_item(self, queue_name: str, item_id: str, error_message: str = "") -> bool:
        """
        Retry a failed item by moving it back to the queue or dead letter queue.
        
        Args:
            queue_name: Name of the queue
            item_id: ID of the item to retry
            error_message: Error message for logging
            
        Returns:
            True if item was retried, False if moved to dead letter queue
        """
        try:
            processing_key = self.processing_key.format(queue_name=queue_name)
            
            # Get item from processing queue
            processing_data = self.redis.hget(processing_key, item_id)
            if not processing_data:
                self.logger.warning(f"Item {item_id} not found in processing queue {queue_name}")
                return False
                
            processing_info = json.loads(processing_data)
            item_dict = json.loads(processing_info['item'])
            
            # Convert priority back to enum
            if isinstance(item_dict['priority'], int):
                item_dict['priority'] = QueuePriority(item_dict['priority'])
            else:
                item_dict['priority'] = QueuePriority[item_dict['priority']]
                
            queue_item = QueueItem(**item_dict)
            queue_item.retry_count += 1
            
            # Remove from processing queue
            self.redis.hdel(processing_key, item_id)
            
            if queue_item.retry_count <= queue_item.max_retries:
                # Retry: add back to queue with delay
                await asyncio.sleep(min(2 ** queue_item.retry_count, 60))  # Exponential backoff
                await self.add_to_queue(queue_name, queue_item)
                
                await self._update_stats(queue_name, 'items_retried', 1)
                self.logger.info(f"Retrying item {item_id} (attempt {queue_item.retry_count})")
                return True
            else:
                # Move to dead letter queue
                await self._move_to_dead_letter(queue_name, queue_item, error_message)
                
                await self._update_stats(queue_name, 'items_failed', 1)
                self.logger.error(f"Item {item_id} moved to dead letter queue after {queue_item.retry_count} retries")
                return False
                
        except Exception as e:
            self.logger.error(f"Failed to retry item {item_id} in queue {queue_name}: {e}")
            return False
            
    async def _move_to_dead_letter(self, queue_name: str, queue_item: QueueItem, error_message: str):
        """Move an item to the dead letter queue."""
        try:
            dead_letter_key = self.dead_letter_key.format(queue_name=queue_name)
            
            dead_letter_item = {
                'original_item': asdict(queue_item),
                'error_message': error_message,
                'failed_at': datetime.now().isoformat(),
                'final_retry_count': queue_item.retry_count
            }
            
            self.redis.lpush(dead_letter_key, json.dumps(dead_letter_item))
            
        except Exception as e:
            self.logger.error(f"Failed to move item to dead letter queue: {e}")
            
    async def get_queue_size(self, queue_name: str) -> int:
        """Get the current size of a queue."""
        try:
            queue_key = self.queue_key.format(queue_name=queue_name)
            return self.redis.llen(queue_key)
        except Exception as e:
            self.logger.error(f"Failed to get queue size for {queue_name}: {e}")
            return 0
            
    async def get_processing_count(self, queue_name: str) -> int:
        """Get the number of items currently being processed."""
        try:
            processing_key = self.processing_key.format(queue_name=queue_name)
            return self.redis.hlen(processing_key)
        except Exception as e:
            self.logger.error(f"Failed to get processing count for {queue_name}: {e}")
            return 0
            
    async def get_dead_letter_count(self, queue_name: str) -> int:
        """Get the number of items in the dead letter queue."""
        try:
            dead_letter_key = self.dead_letter_key.format(queue_name=queue_name)
            return self.redis.llen(dead_letter_key)
        except Exception as e:
            self.logger.error(f"Failed to get dead letter count for {queue_name}: {e}")
            return 0
            
    async def get_queue_stats(self, queue_name: str) -> Dict[str, Any]:
        """Get comprehensive statistics for a queue."""
        try:
            stats_key = self.stats_key.format(queue_name=queue_name)
            stats = self.redis.hgetall(stats_key)
            
            # Convert string values to integers where appropriate
            for key, value in stats.items():
                if key.startswith('items_'):
                    stats[key] = int(value) if value else 0
                    
            # Add current queue sizes
            stats.update({
                'current_queue_size': await self.get_queue_size(queue_name),
                'current_processing_count': await self.get_processing_count(queue_name),
                'current_dead_letter_count': await self.get_dead_letter_count(queue_name),
                'last_updated': datetime.now().isoformat()
            })
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Failed to get stats for queue {queue_name}: {e}")
            return {}
            
    async def _update_stats(self, queue_name: str, stat_name: str, increment: int = 1):
        """Update queue statistics."""
        try:
            stats_key = self.stats_key.format(queue_name=queue_name)
            self.redis.hincrby(stats_key, stat_name, increment)
            self.redis.hset(stats_key, 'last_updated', datetime.now().isoformat())
            
        except Exception as e:
            self.logger.error(f"Failed to update stats for {queue_name}: {e}")
            
    async def _register_queue(self, queue_name: str):
        """Register a queue in the global registry."""
        try:
            registry_key = f"{self.namespace}:queue_registry"
            self.redis.sadd(registry_key, queue_name)
            
        except Exception as e:
            self.logger.error(f"Failed to register queue {queue_name}: {e}")
            
    async def list_queues(self) -> List[str]:
        """List all registered queues."""
        try:
            registry_key = f"{self.namespace}:queue_registry"
            return list(self.redis.smembers(registry_key))
            
        except Exception as e:
            self.logger.error(f"Failed to list queues: {e}")
            return []
            
    async def purge_queue(self, queue_name: str) -> bool:
        """Purge all items from a queue (use with caution)."""
        try:
            queue_key = self.queue_key.format(queue_name=queue_name)
            processing_key = self.processing_key.format(queue_name=queue_name)
            
            # Clear main queue and processing queue
            self.redis.delete(queue_key)
            self.redis.delete(processing_key)
            
            self.logger.warning(f"Purged queue {queue_name}")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to purge queue {queue_name}: {e}")
            return False
